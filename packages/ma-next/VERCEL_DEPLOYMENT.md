# Vercel Deployment Configuration

This project has been configured to deploy to Vercel instead of Netlify.

## Changes Made

1. **API Functions**: Moved from `netlify/functions/` to `api/` directory to follow Vercel's conventions
2. **Configuration**: Added `vercel.json` for Vercel-specific settings
3. **Dependencies**: 
   - Removed `@netlify/functions`
   - Added `@vercel/node` and `vercel` CLI
4. **Environment Variables**: Updated from `NETLIFY_DEV` to `VERCEL_ENV`
5. **Scripts**: Changed `netlify` script to `vercel` in package.json

## Deployment Steps

### Option 1: Automatic Deployment via Git

1. Connect your GitHub repository to Vercel:
   - Go to [vercel.com](https://vercel.com) and sign in
   - Click "New Project" and import your GitHub repository
   - Select the `packages/ma-next` folder as the root directory
   - Configure environment variables in the Vercel dashboard
   - Deploy automatically on every push to main branch

### Option 2: Manual Deployment via CLI

1. Install Vercel CLI globally:
   ```bash
   npm install -g vercel
   ```

2. Navigate to the ma-next directory:
   ```bash
   cd packages/ma-next
   ```

3. Login to Vercel:
   ```bash
   vercel login
   ```

4. Deploy:
   ```bash
   vercel --prod
   ```

## Environment Variables

Make sure to configure the following environment variables in Vercel:

- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `NANGO_SECRET_KEY`
- `NANGO_PUBLIC_KEY`
- `OPENAI_API_KEY`
- Any other environment variables your app requires

## Local Development

To run the project locally with Vercel's development server:

```bash
pnpm run vercel
```

This will start the Vercel development server which simulates the production environment.

## API Routes

All API routes are now located in the `api/` directory and will be automatically deployed as Vercel Functions.

## Differences from Netlify

- **Function Runtime**: Uses Vercel's serverless functions instead of Netlify Functions
- **Background Tasks**: No `context.waitUntil()` equivalent - background tasks run as fire-and-forget promises
- **Environment Detection**: Uses `VERCEL_ENV` instead of `NETLIFY_DEV`
- **Build Process**: Optimized for Vercel's build system
