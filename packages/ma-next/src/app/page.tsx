'use client';

import { AlphaInfoPanel, PromptInput } from 'components/conversation';
import { useRouter } from 'next/navigation';
import { useChatContext } from 'providers/ChatContext';
import { ROUTES } from 'src/config';
import { getCurrentUserId, supabase } from 'lib/supabase';
import Link from 'next/link';

export default function Chat() {
  const router = useRouter();
  const chat = useChatContext();

  return (
    <div className="p-6 min-h-screen flex flex-col">
      <div className="max-w-3xl mx-auto flex-1 flex flex-col items-center justify-center">
        <AlphaInfoPanel />
        <PromptInput
          {...chat}
          handleSubmit={async e => {
            if (!chat.input.trim()) return;

            let userId = await getCurrentUserId();

            if (!userId) {
              const { data, error } = await supabase.auth.signInAnonymously();

              if (error) {
                throw error;
              }

              if (!data?.user?.id) {
                throw new Error('Failed to get user ID');
              }

              userId = data.user.id;
            }

            // HACK: we need to fix the actual useChat to only display messages from a given chat.
            chat.setMessages([]);
            chat.handleSubmit(e);
            router.push(ROUTES.chat(chat.id));
          }}
          isNewChat
        />
      </div>

      {/* Footer with Terms and Privacy Policy notice */}
      <footer className="mt-auto pb-4">
        <div className="max-w-3xl mx-auto text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            By messaging MakeAgent, you agree to our{' '}
            <Link
              href="/terms"
              className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 hover:underline"
            >
              Terms
            </Link>{' '}
            and have read our{' '}
            <Link
              href="/privacy-policy"
              className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 hover:underline"
            >
              Privacy Policy
            </Link>
            .
          </p>
        </div>
      </footer>
    </div>
  );
}
