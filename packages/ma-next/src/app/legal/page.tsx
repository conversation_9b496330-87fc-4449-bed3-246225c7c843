import Link from 'next/link';
import type { Metadata } from 'next';
import { FileText, Shield } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Legal Information',
};

export default function LegalPage() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-16">
      <div className="rounded-2xl shadow-sm p-8 md:p-12 bg-white dark:bg-gray-800">
        <h1 className="text-4xl font-bold text-center text-gray-900 dark:text-white mb-4">
          Legal Information
        </h1>
        <p className="text-lg text-center text-gray-600 dark:text-gray-400 mb-12">
          Important information about your rights and our policies
        </p>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Terms of Service Card */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg">
                <FileText className="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Terms of Service
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Our Terms of Service outline the rules and regulations for using MakeAgent. 
              This includes account responsibilities, acceptable use policies, and our 
              service commitments to you.
            </p>
            <div className="space-y-3 mb-6">
              <h3 className="font-medium text-gray-900 dark:text-white">Key topics covered:</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-center gap-2">
                  <span className="w-1.5 h-1.5 bg-indigo-500 rounded-full" />
                  Account terms and responsibilities
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-1.5 h-1.5 bg-indigo-500 rounded-full" />
                  Acceptable use policies
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-1.5 h-1.5 bg-indigo-500 rounded-full" />
                  Third-party integrations
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-1.5 h-1.5 bg-indigo-500 rounded-full" />
                  Intellectual property rights
                </li>
              </ul>
            </div>
            <Link
              href="/terms"
              className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Read Terms of Service
            </Link>
          </div>

          {/* Privacy Policy Card */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <Shield className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Privacy Policy
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Our Privacy Policy explains how we collect, use, and protect your personal 
              information. We're committed to transparency about our data practices and 
              your privacy rights.
            </p>
            <div className="space-y-3 mb-6">
              <h3 className="font-medium text-gray-900 dark:text-white">Key topics covered:</h3>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-center gap-2">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                  Data collection and usage
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                  Third-party data sharing
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                  Your privacy rights
                </li>
                <li className="flex items-center gap-2">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                  Cookie and tracking policies
                </li>
              </ul>
            </div>
            <Link
              href="/privacy-policy"
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Read Privacy Policy
            </Link>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
            Questions about our policies?
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            If you have any questions about our Terms of Service or Privacy Policy, 
            we're here to help.
          </p>
          <a
            href="mailto:<EMAIL>"
            className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Contact Support
          </a>
        </div>

        {/* Back Button */}
        <div className="mt-12 text-center">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
          >
            Back to MakeAgent
          </Link>
        </div>
      </div>
    </div>
  );
}
