import { useRef, useEffect, useState } from 'react';
import {
  ChevronDown,
  LayoutGrid,
  User,
  HelpCircle,
  MessageSquare,
  LogOut,
  Link2,
  FileText,
  Shield,
} from 'lucide-react';
import { useAuth } from 'hooks/useAuth';
import { useProfile } from 'hooks/useProfile';
import { M } from 'intl';
import { ProfileModal } from '../modals/ProfileModal';
import { ConnectionsListModal } from '../modals';
import { useModal } from 'hooks/useModal';
import Link from 'next/link';

interface UserMenuProps {
  onManageAgents: () => void;
}

function UserMenu({ onManageAgents }: UserMenuProps) {
  const { user, logout } = useAuth();
  const { profile } = useProfile();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);
  const { openModal, isOpen } = useModal();

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <>
      <ProfileModal />
      <ConnectionsListModal isOpen={isOpen('connections')} />

      <div className="relative" ref={userMenuRef}>
        <button
          onClick={() => setShowUserMenu(!showUserMenu)}
          className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <div className="w-8 h-8 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center text-indigo-600 dark:text-indigo-300">
            {profile?.firstName
              ? profile.firstName[0].toUpperCase()
              : user?.email?.[0].toUpperCase()}
          </div>
          <ChevronDown className="w-4 h-4" />
        </button>

        {showUserMenu && (
          <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg py-1">
            <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="text-sm text-gray-500 dark:text-gray-400 truncate">{user?.email}</div>
            </div>

            <button
              onClick={() => {
                setShowUserMenu(false);
                onManageAgents();
              }}
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
            >
              <LayoutGrid className="w-4 h-4" />
              <M id="navbar.myAgents" />
            </button>

            <button
              onClick={() => {
                setShowUserMenu(false);
                openModal('connections');
              }}
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
            >
              <Link2 className="w-4 h-4" />
              Connections
            </button>

            <button
              onClick={() => {
                setShowUserMenu(false);
                openModal('profile');
              }}
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
            >
              <User className="w-4 h-4" />
              <M id="navbar.profile" />
            </button>

            <button
              onClick={() => {
                setShowUserMenu(false);
                // Open Help & Feedback
              }}
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
            >
              <HelpCircle className="w-4 h-4" />
              <M id="navbar.help" />
            </button>

            <button
              onClick={() => {
                setShowUserMenu(false);
                // Open Feedback
              }}
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
            >
              <MessageSquare className="w-4 h-4" />
              <M id="navbar.feedback" />
            </button>

            <hr className="my-1 border-gray-200 dark:border-gray-700" />

            <Link
              href="/legal"
              onClick={() => setShowUserMenu(false)}
              className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
            >
              <FileText className="w-4 h-4" />
              Terms & Privacy
            </Link>

            <hr className="my-1 border-gray-200 dark:border-gray-700" />

            <button
              onClick={() => {
                setShowUserMenu(false);
                logout();
              }}
              className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
            >
              <LogOut className="w-4 h-4" />
              <M id="auth.logout" />
            </button>
          </div>
        )}
      </div>
    </>
  );
}

export { UserMenu };
